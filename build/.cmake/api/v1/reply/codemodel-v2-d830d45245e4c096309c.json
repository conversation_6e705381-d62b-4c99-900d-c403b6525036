{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "RayTracing", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "RayTracing::@6890427a1f51a3e7e1df", "jsonFile": "target-RayTracing-Debug-01a1089053a9a5e80b1a.json", "name": "RayTracing", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/usr/games/Assignment7/build", "source": "/usr/games/Assignment7"}, "version": {"major": 2, "minor": 6}}