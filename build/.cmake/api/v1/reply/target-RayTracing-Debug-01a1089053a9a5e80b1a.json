{"artifacts": [{"path": "RayTracing"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 6, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 2, 7, 11, 17]}], "id": "RayTracing::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}], "language": "CXX"}, "name": "RayTracing", "nameOnDisk": "RayTracing", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 7, 11, 17]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 5, 6, 8, 9, 10, 12, 13, 14, 15, 16, 18]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Object.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Vector.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Vector.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Sphere.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "global.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Triangle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Scene.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Scene.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Light.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "AreaLight.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "BVH.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "BVH.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Bounds3.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Ray.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Material.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Intersection.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Renderer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Renderer.hpp", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}