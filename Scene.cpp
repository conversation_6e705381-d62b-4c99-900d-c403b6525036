//
// Created by <PERSON><PERSON><PERSON><PERSON> on 2019-05-14.
//

#include "Scene.hpp"

void Scene::buildBVH()
{
    printf(" - Generating BVH...\n\n");
    this->bvh = new BVHAccel(objects, 1, BVHAccel::SplitMethod::NAIVE);
}

Intersection Scene::intersect(const Ray &ray) const
{
    return this->bvh->Intersect(ray);
}

void Scene::sampleLight(Intersection &pos, float &pdf) const
{
    float emit_area_sum = 0;
    for (uint32_t k = 0; k < objects.size(); ++k)
    {
        if (objects[k]->hasEmit())
        {
            emit_area_sum += objects[k]->getArea();
        }
    }
    float p = get_random_float() * emit_area_sum;
    emit_area_sum = 0;
    for (uint32_t k = 0; k < objects.size(); ++k)
    {
        if (objects[k]->hasEmit())
        {
            emit_area_sum += objects[k]->getArea();
            if (p <= emit_area_sum)
            {
                objects[k]-><PERSON><PERSON>(pos, pdf);
                break;
            }
        }
    }
}

bool Scene::trace(
    const Ray &ray,
    const std::vector<Object *> &objects,
    float &tNear, uint32_t &index, Object **hitObject)
{
    *hitObject = nullptr;
    for (uint32_t k = 0; k < objects.size(); ++k)
    {
        float tNearK = kInfinity;
        uint32_t indexK;
        Vector2f uvK;
        if (objects[k]->intersect(ray, tNearK, indexK) && tNearK < tNear)
        {
            *hitObject = objects[k];
            tNear = tNearK;
            index = indexK;
        }
    }

    return (*hitObject != nullptr);
}

// Implementation of Path Tracing
Vector3f Scene::castRay(const Ray &ray, int depth) const
{
    // Pesudo code of Path Tracing Algorithm
    // shade(p, wo)
    //     sampleLight(inter , pdf_light)
    //     Get x, ws, NN, emit from inter
    //     Shoot a ray from p to x
    //     If the ray is not blocked in the middle
    //     L_dir = emit * eval(wo, ws, N) * dot(ws, N) * dot(ws,NN) / |x-p|^2 / pdf_light

    //     _indir = 0.0
    //     Test Russian Roulette with probability RussianRoulette
    //     wi = sample(wo, N)
    //     Trace a ray r(p, wi)
    //     If ray r hit a non-emitting object at q
    //     L_indir = shade(q, wi) * eval(wo, wi, N) * dot(wi, N)/ pdf(wo, wi, N) / RussianRoulette

    //     Return L_dir + L_indir

    // TO DO Implement Path Tracing Algorithm here

    Intersection inter = intersect(ray);
    if (!inter.happened)
        return backgroundColor;
    if (inter.m->hasEmission())
        return inter.m->getEmission();

    // sample a random light source
    Intersection pos;
    float pdf;
    sampleLight(pos, pdf);
    Vector3f emit = pos.emit;
    Vector3f ws = pos.coords - inter.coords;
    float dis2 = dotProduct(ws, ws);
    ws = normalize(ws);
    float cos_ws = dotProduct(inter.normal, ws);
    float cos_ws2 = dotProduct(pos.normal, -ws);
    if (cos_ws <= 0 || cos_ws2 <= 0)
        return Vector3f(0.0f);
    Ray ray2(inter.coords, ws);
    if (intersect(ray2).happened)
        return Vector3f(0.0f);
    Vector3f L_dir = emit * inter.m->eval(ray.direction, ws, inter.normal) * cos_ws * cos_ws2 / dis2 / pdf;

    // Russian Roulette
    if (get_random_float() > RussianRoulette)
        return L_dir;

    // sample a random direction
    Vector3f wi = inter.m->sample(ray.direction, inter.normal);
    float pdf_wi = inter.m->pdf(ray.direction, wi, inter.normal);
    if (pdf_wi == 0)
        return L_dir;
    Ray ray3(inter.coords, wi);
    Intersection inter3 = intersect(ray3);
    if (!inter3.happened || inter3.m->hasEmission())
        return L_dir;
    float cos_wi = dotProduct(inter3.normal, -wi);
    if (cos_wi <= 0)
        return L_dir;
    Vector3f L_indir = castRay(ray3, depth + 1) * inter.m->eval(ray.direction, wi, inter.normal) * cos_wi / pdf_wi / RussianRoulette;
    return L_dir + L_indir;

}